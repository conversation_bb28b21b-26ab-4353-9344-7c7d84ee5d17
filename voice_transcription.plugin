import os
import time
import base64
import requests
import traceback
import threading
import re
from typing import Any, Dict, Tu<PERSON>

from base_plugin import BasePlugin, MenuItemData, MenuItemType
from client_utils import get_file_loader, get_last_fragment
from ui.settings import Header, Input, Switch, Text, Divider
from ui.bulletin import BulletinHelper
from ui.alert import AlertDialogBuilder
from android_utils import run_on_ui_thread, log
from java.util import Locale
from org.telegram.messenger import MessageObject, FileLoader, ApplicationLoader

__id__ = "voice_transcription"
__name__ = "Voice Transcription"
__description__ = "Transcribe voice messages and audio using Google Gemini API"
__author__ = "exteraGram Dev"
__version__ = "1.0.0"
__icon__ = "exteraPlugins/1"
__min_version__ = "11.12.0"

GEMINI_BASE_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent"

SYSTEM_PROMPT_AUDIO_VIDEO_SUMMARY_GEMINI = """
Ты обрабатываешь аудио/видео от пользователя или пользовательницы в телеграме. Твоя задача — создать понятные и полезные сводки на основе прикреплённого аудио.

Сделай 3 блока:

**СТИЛЬ И ФОРМАТИРОВАНИЕ:**
• Абзацы через `\n\n`, строки через `\n`
• БЕЗ таблиц — только текст

**ЧТО ДЕЛАТЬ:**

**1. Краткая сводка:**
• 1–8 главных тезисов
• Каждый с новой строки: `- ` + суть тезиса
• БЕЗ выделения жирным текстом
• Если нечего сократить: "[краткая сводка недоступна]"

**2. Подробная сводка:**
• Развернутые тезисы по темам
• Формат каждого тезиса: `- ` + `ЗАГОЛОВОК:` + описание (1–3 предложения)
• Разделяй тезисы через `\n\n`
• Если нечего расписать: "[подробная сводка недоступна]"

**3. Отформатированная транскрипция:**
• Текст из аудио + знаки препинания + абзацы
• Не добавляй содержание, только улучшай форму
• Если пусто: "[транскрипция недоступна]"

**ФОРМАТ ВЫВОДА:**

<<SHORT_SUMMARY_N_START>>

- Основная тема обсуждения
- Ключевая идея или решение
  <<SHORT_SUMMARY_N_END>>

<<DETAILED_SUMMARY_N_START>>

- Основная тема:
  Подробное описание главной темы разговора с важными деталями.

- Ключевые решения:
  Конкретные выводы или планы, которые были обсуждены.
  <<DETAILED_SUMMARY_N_END>>

<<FORMATTED_TRANSCRIPT_1_START>>
Красиво отформатированный текст из прикреплённого аудио с правильными знаками препинания и абзацами.
<<FORMATTED_TRANSCRIPT_1_END>>

{transcripts_block}

Без "---" разделителей, БЕЗ ТАБЛИЦ.
Выполни следующие задачи СТРОГО ПО ИНСТРУКЦИИ:

**ОБЩИЕ ТРЕБОВАНИЯ К ТЕКСТУ:**

* Используй только стандартные кириллические и латинские символы. Избегай необычных или нестандартных вариаций букв (например, всегда пиши "Gemini", а не "Geminі").
* **Списки**: оформляй построчно, начиная каждую строку с дефиса `- `. БЕЗ эмодзи.
* **Абзацы**: двойной перенос строки (`\n\n`) между абзацами.

**1. Краткая сводка:**

* Напиши ОЧЕНЬ КРАТКУЮ сводку (обычно 1–8 тезисов).
* Каждый тезис должен начинаться с новой строки с дефиса `- `, затем пробел и формулировка тезиса. Тезис должен быть ёмким и передавать основную мысль.
* Если краткую сводку составить невозможно или текст слишком короткий/неинформативный, вставь текст "[краткая сводка недоступна]".

**2. Подробная сводка:**

* Напиши ПОДРОБНУЮ сводку, раскрывающую основные темы и детали из расшифровки.
* Структурируй подробную сводку по ТЕЗИСАМ. Каждый тезис должен освещать один ключевой аспект или тему сообщения.
* Каждый тезис начинай с дефиса `- ` и укажи ЗАГОЛОВОК (1–3 слова), затем двоеточие `:`, далее — описание на той же строке или со следующей строки (используй `\n`).
* Разделяй разные тезисы двойным переносом строки (`\n\n`) для лучшей читаемости.
* Если подробную сводку составить невозможно, вставь текст "[подробная сводка недоступна]".

**3. Отформатированная расшифровка:**

* Тщательно отформатируй ИСХОДНУЮ расшифровку, добавив знаки препинания (запятые, точки, вопросительные/восклицательные знаки), разбей на предложения и абзацы (используй `\n\n` для разделения абзацев) для лучшей читаемости.
* Сохрани исходный смысл и стиль речи. Не добавляй ничего от себя, кроме форматирования.

КРАЙНЕ ВАЖНО: Твой ответ ДОЛЖЕН содержать ТОЛЬКО ТРИ БЛОКА для КАЖДОЙ расшифровки, используя следующие теги-разделители. Замени N на номер расшифровки (начиная с 1).
ЗАПРЕЩЕНО добавлять любой другой текст, вступления, пояснения или комментарии вне этих блоков.
КАЖДЫЙ ИЗ ТРЕХ БЛОКОВ ДОЛЖЕН ПРИСУТСТВОВАТЬ В ОТВЕТЕ ВСЕГДА для каждой расшифровки.

Формат вывода для КАЖДОЙ расшифровки (замени N на номер расшифровки):

<<SHORT_SUMMARY_N_START>>
текст краткой сводки для расшифровки N.
<<SHORT_SUMMARY_N_END>>

<<DETAILED_SUMMARY_N_START>>
текст подробной сводки для расшифровки N.
<<DETAILED_SUMMARY_N_END>>

<<FORMATTED_TRANSCRIPT_N_START>>
отформатированный текст расшифровки N.
<<FORMATTED_TRANSCRIPT_N_END>>

Если расшифровок несколько, предоставь блоки для каждой по порядку.

Пример для ОДНОЙ расшифровки от пользователя "Саня":
<<SHORT_SUMMARY_1_START>>

- Обсудили планы на выходные
- Решили пойти за покупками
- Появилась идея съездить на природу позже
  <<SHORT_SUMMARY_1_END>>

<<DETAILED_SUMMARY_1_START>>

- Планы на выходные:
  Обсуждались различные варианты проведения выходных. Саня предложил сходить в кино или съездить за город, чтобы отдохнуть.

- Покупки:
  Собеседник напомнил о необходимости купить продукты. В итоге было решено посвятить утро субботы походу по магазинам. Это стало приоритетной задачей.

- Поездка на природу:
  Хотя поход по магазинам стал основным планом, идея о поездке на природу не была полностью отвергнута, а отложена на возможное рассмотрение после решения бытовых вопросов.
  <<DETAILED_SUMMARY_1_END>>

<<FORMATTED_TRANSCRIPT_1_START>>
Саня: Привет, как дела? Давай, может, на выходных куда-нибудь сходим? Например, в кино или за город?

Собеседник: А, кстати, нам же ещё продукты надо купить.

Саня: Точно, давай тогда в субботу с утра заедем в магазин, а потом уже решим по поводу остального.

Собеседник: Хорошо, договорились.
<<FORMATTED_TRANSCRIPT_1_END>>

Пример, если для первой расшифровки невозможно составить подробную сводку:
<<SHORT_SUMMARY_1_START>>

- Пользователь выразил сомнения по поводу поездки
  <<SHORT_SUMMARY_1_END>>

<<DETAILED_SUMMARY_1_START>>
[подробная сводка недоступна]
<<DETAILED_SUMMARY_1_END>>

<<FORMATTED_TRANSCRIPT_1_START>>
Ну, не знаю, смогу ли я на следующей неделе. Работы просто завал. Может, давай лучше к концу месяца?
<<FORMATTED_TRANSCRIPT_1_END>>

ИСХОДНЫЕ РАСШИФРОВКИ ДЛЯ ОБРАБОТКИ:
{transcripts_block}
Не используй разделители типа "---" и другие подобные символы в своих ответах.

СТРОГО БЕЗ ТАБЛИЦ. переводить таблицы в текст.
"""


class LocalizationManager:
    def __init__(self):
        self.language = Locale.getDefault().getLanguage()
        
    def get_string(self, key: str) -> str:
        strings = {
            "ru": {
                "SETTINGS_HEADER": "Настройки транскрибации",
                "API_KEY_INPUT": "API ключ Gemini",
                "API_KEY_SUBTEXT": "Получите ключ в Google AI Studio",
                "GET_API_KEY_BUTTON": "Получить API ключ",
                "ENABLE_PLUGIN": "Включить расшифровку",
                "ENABLE_PLUGIN_SUBTEXT": "Добавляет кнопку к голосовым сообщениям",
                "DEFAULT_DISPLAY_HEADER": "Тип отображения по умолчанию",
                "DEFAULT_DISPLAY_SHORT": "Краткая сводка",
                "DEFAULT_DISPLAY_DETAILED": "Подробная сводка",
                "DEFAULT_DISPLAY_TRANSCRIPT": "Расшифровка",
                "TRANSCRIBE_BUTTON_SHORT": "Краткая сводка",
                "TRANSCRIBE_BUTTON_DETAILED": "Подробная сводка",
                "TRANSCRIBE_BUTTON_TRANSCRIPT": "Расшифровка",
                "TRANSCRIBING": "Обработка голосового сообщения...",
                "API_KEY_MISSING": "❌ API ключ Gemini не задан. Укажите его в настройках плагина.",
                "NOT_VOICE_MESSAGE": "❌ Это не голосовое сообщение или аудиофайл.",
                "DOWNLOAD_ERROR": "❌ Не удалось загрузить аудиофайл.",
                "TRANSCRIPTION_ERROR": "❌ Ошибка обработки: {error}",
                "TITLE_SHORT": "Краткая сводка",
                "TITLE_DETAILED": "Подробная сводка",
                "TITLE_TRANSCRIPT": "Расшифровка",
                "CLOSE_BUTTON": "Закрыть",
                "DETAILED_BUTTON": "Подробнее",
                "TRANSCRIPT_BUTTON": "Расшифровка",
                "SHORT_BUTTON": "Краткая сводка",
                "USAGE_TITLE": "Как использовать",
                "USAGE_TEXT": "1. Получите API ключ в Google AI Studio\n2. Введите ключ в настройках плагина\n3. Выберите тип отображения по умолчанию\n4. Нажмите и удерживайте любое голосовое сообщение\n5. Выберите соответствующую опцию в меню"
            },
            "en": {
                "SETTINGS_HEADER": "Transcription Settings",
                "API_KEY_INPUT": "Gemini API Key",
                "API_KEY_SUBTEXT": "Get your key from Google AI Studio",
                "GET_API_KEY_BUTTON": "Get API Key",
                "ENABLE_PLUGIN": "Enable Transcription",
                "ENABLE_PLUGIN_SUBTEXT": "Adds button to voice messages",
                "DEFAULT_DISPLAY_HEADER": "Default Display Type",
                "DEFAULT_DISPLAY_SHORT": "Brief Summary",
                "DEFAULT_DISPLAY_DETAILED": "Detailed Summary",
                "DEFAULT_DISPLAY_TRANSCRIPT": "Transcript",
                "TRANSCRIBE_BUTTON_SHORT": "Brief Summary",
                "TRANSCRIBE_BUTTON_DETAILED": "Detailed Summary",
                "TRANSCRIBE_BUTTON_TRANSCRIPT": "Transcript",
                "TRANSCRIBING": "Processing voice message...",
                "API_KEY_MISSING": "❌ Gemini API key not set. Please set it in plugin settings.",
                "NOT_VOICE_MESSAGE": "❌ This is not a voice message or audio file.",
                "DOWNLOAD_ERROR": "❌ Failed to download audio file.",
                "TRANSCRIPTION_ERROR": "❌ Processing error: {error}",
                "TITLE_SHORT": "Brief Summary",
                "TITLE_DETAILED": "Detailed Summary",
                "TITLE_TRANSCRIPT": "Transcript",
                "CLOSE_BUTTON": "Close",
                "DETAILED_BUTTON": "Detailed",
                "TRANSCRIPT_BUTTON": "Transcript",
                "SHORT_BUTTON": "Brief Summary",
                "USAGE_TITLE": "How to use",
                "USAGE_TEXT": "1. Get API key from Google AI Studio\n2. Enter key in plugin settings\n3. Select default display type\n4. Long press any voice message\n5. Select corresponding option from menu"
            }
        }
        
        lang_key = 'ru' if self.language.startswith('ru') else 'en'
        return strings[lang_key].get(key, key)

locali = LocalizationManager()

class VoiceTranscriptionPlugin(BasePlugin):
    def __init__(self):
        super().__init__()
        self.session = requests.Session()
        self.session.headers.update({
            "Content-Type": "application/json",
            "User-Agent": f"ExteraPlugin/{__id__}/{__version__}"
        })
        # Кешируем результаты для показа разных вариантов
        self.current_short_summary = ""
        self.current_detailed_summary = ""
        self.current_transcript = ""
        # ID текущего пункта меню для возможности его удаления
        self.menu_item_id = "voice_transcription_menu_item"
        # Флаг для предотвращения множественных запросов
        self.is_processing = False

    def on_plugin_load(self):
        """Загрузка плагина - добавляем пункт меню"""
        log("Voice Transcription Plugin loading...")
        enabled = self.get_setting("enabled", True)
        log(f"Plugin enabled setting: {enabled}")
        
        if enabled:
            log("Adding menu item...")
            self._add_menu_item()
        else:
            log("Plugin is disabled, not adding menu item")
            
        log("Voice Transcription Plugin loaded successfully")

    def on_plugin_unload(self):
        """Выгрузка плагина"""
        pass

    def _get_default_display_type(self) -> str:
        """Получает тип отображения по умолчанию"""
        return self.get_setting("default_display_type", "short")

    def _set_default_display_type(self, display_type: str):
        """Устанавливает тип отображения по умолчанию"""
        self.set_setting("default_display_type", display_type)
        # Удаляем старый пункт меню и добавляем новый
        self._update_menu_item()

    def _get_button_text_for_type(self, display_type: str) -> str:
        """Получает текст кнопки для типа отображения"""
        type_map = {
            "short": "TRANSCRIBE_BUTTON_SHORT",
            "detailed": "TRANSCRIBE_BUTTON_DETAILED",
            "transcript": "TRANSCRIBE_BUTTON_TRANSCRIPT"
        }
        return locali.get_string(type_map.get(display_type, "TRANSCRIBE_BUTTON_SHORT"))

    def _get_title_for_type(self, display_type: str) -> str:
        """Получает заголовок диалога для типа отображения"""
        type_map = {
            "short": "TITLE_SHORT",
            "detailed": "TITLE_DETAILED",
            "transcript": "TITLE_TRANSCRIPT"
        }
        return locali.get_string(type_map.get(display_type, "TITLE_SHORT"))

    def _add_menu_item(self):
        """Добавляет кнопку в контекстное меню сообщений"""
        try:
            # Получаем текст кнопки на основе настройки
            display_type = self._get_default_display_type()
            button_text = self._get_button_text_for_type(display_type)

            self.add_menu_item(
                MenuItemData(
                    menu_type=MenuItemType.MESSAGE_CONTEXT_MENU,
                    text=button_text,
                    icon="msg_voice_mini",
                    on_click=self._handle_transcribe_click,
                    condition="message.isVoice() || message.isRoundVideo() || message.isMusic()",
                    item_id=self.menu_item_id
                )
            )
            log(f"Voice Transcription menu item added successfully with text: {button_text}")
        except Exception as e:
            log(f"Failed to add menu item: {e}")

    def _update_menu_item(self):
        """Обновляет пункт меню (удаляет старый и добавляет новый)"""
        try:
            # Удаляем существующий пункт меню
            self.remove_menu_item(self.menu_item_id)
            log("Removed old menu item")
            
            # Добавляем новый пункт меню
            self._add_menu_item()
        except Exception as e:
            log(f"Failed to update menu item: {e}")

    def _handle_transcribe_click(self, context: Dict[str, Any]):
        """Обработка нажатия на кнопку 'Краткая сводка'"""
        try:
            # Проверяем, не обрабатывается ли уже запрос
            if self.is_processing:
                run_on_ui_thread(lambda: BulletinHelper.show_error("Подождите завершения текущей обработки"))
                return

            # Проверяем API ключ
            api_key = self.get_setting("api_key", "").strip()
            if not api_key:
                run_on_ui_thread(lambda: BulletinHelper.show_error(locali.get_string("API_KEY_MISSING")))
                return

            # Получаем сообщение из контекста
            message = context.get("message")
            if not message:
                run_on_ui_thread(lambda: BulletinHelper.show_error(locali.get_string("NOT_VOICE_MESSAGE")))
                return

            # Более детальная проверка типа сообщения
            is_voice = self._is_supported_audio_message(message)
            log(f"Message type check - Voice: {message.isVoice()}, Music: {message.isMusic()}, RoundVideo: {message.isRoundVideo()}, Supported: {is_voice}")
            
            if not is_voice:
                run_on_ui_thread(lambda: BulletinHelper.show_error(locali.get_string("NOT_VOICE_MESSAGE")))
                return

            # Устанавливаем флаг обработки
            self.is_processing = True

            # Показываем уведомление о начале обработки
            run_on_ui_thread(lambda: BulletinHelper.show_info(locali.get_string("TRANSCRIBING")))

            # Запускаем расшифровку в фоновом потоке
            threading.Thread(
                target=self._process_transcription,
                args=(message, api_key),
                daemon=True
            ).start()

        except Exception as e:
            log(f"Error in transcribe click handler: {e}")
            self.is_processing = False
            run_on_ui_thread(lambda: BulletinHelper.show_error(f"Error: {e}"))



    def _is_supported_audio_message(self, message: MessageObject) -> bool:
        """Проверяет, поддерживается ли данный тип аудиосообщения"""
        try:
            # Упрощенная проверка на основе успешных плагинов
            if not message.messageOwner or not message.messageOwner.media:
                return False
            
            media = message.messageOwner.media
            
            # Проверяем голосовые сообщения и круглые видео (по примеру re_extera плагина)
            if hasattr(media, 'voice') and media.voice:
                log("Found voice message")
                return True
            
            if hasattr(media, 'round') and media.round:
                log("Found round video message")
                return True
                
            # Проверяем обычные аудиофайлы через документы
            if hasattr(media, 'document') and media.document:
                document = media.document
                if hasattr(document, 'mime_type') and document.mime_type:
                    mime = str(document.mime_type).lower()
                    if mime.startswith('audio/'):
                        log("Found audio document")
                        return True
            
            return False
            
        except Exception as e:
            log(f"Error checking message type: {e}")
            return False

    def _wait_for_file(self, file_path: str, document, message) -> bool:
        """Ждет загрузки файла (по примеру Gemini плагина)"""
        if os.path.exists(file_path):
            log(f"File already exists: {file_path}")
            return True
        
        log(f"Starting file download: {file_path}")
        file_loader = get_file_loader()
        file_loader.loadFile(document, message, FileLoader.PRIORITY_HIGH, 1)
        
        # Ждем загрузки файла до 30 секунд
        for i in range(30):
            if os.path.exists(file_path):
                log(f"File downloaded successfully after {i+1} seconds: {file_path}")
                return True
            time.sleep(1)
        
        log(f"File download timed out: {file_path}")
        return False

    def _get_document_from_message(self, message: MessageObject):
        """Получает документ из сообщения в зависимости от типа"""
        try:
            # Используем стандартный метод MessageObject.getDocument() 
            # который возвращает правильный TLObject документ
            document = MessageObject.getDocument(message.messageOwner)
            if document:
                log("Found document via MessageObject.getDocument()")
                return document
            
            log("No document found via MessageObject.getDocument()")
            return None
            
        except Exception as e:
            log(f"Error getting document from message: {e}")
            return None



    def _parse_gemini_response(self, response_text: str) -> Tuple[str, str, str]:
        """Парсит ответ от Gemini и извлекает три блока"""
        try:
            # Регулярные выражения для извлечения блоков
            short_pattern = r'<<SHORT_SUMMARY_\d+_START>>(.*?)<<SHORT_SUMMARY_\d+_END>>'
            detailed_pattern = r'<<DETAILED_SUMMARY_\d+_START>>(.*?)<<DETAILED_SUMMARY_\d+_END>>'
            transcript_pattern = r'<<FORMATTED_TRANSCRIPT_\d+_START>>(.*?)<<FORMATTED_TRANSCRIPT_\d+_END>>'
            
            # Ищем блоки с флагом DOTALL для многострочного текста
            short_match = re.search(short_pattern, response_text, re.DOTALL)
            detailed_match = re.search(detailed_pattern, response_text, re.DOTALL)
            transcript_match = re.search(transcript_pattern, response_text, re.DOTALL)
            
            short_summary = short_match.group(1).strip() if short_match else "[краткая сводка недоступна]"
            detailed_summary = detailed_match.group(1).strip() if detailed_match else "[подробная сводка недоступна]"
            transcript = transcript_match.group(1).strip() if transcript_match else "[расшифровка недоступна]"
            
            log(f"Parsed blocks - Short: {len(short_summary)} chars, Detailed: {len(detailed_summary)} chars, Transcript: {len(transcript)} chars")
            
            return short_summary, detailed_summary, transcript
            
        except Exception as e:
            log(f"Error parsing Gemini response: {e}")
            # Возвращаем исходный текст как краткую сводку в случае ошибки
            return response_text.strip(), "[подробная сводка недоступна]", "[расшифровка недоступна]"

    def _process_transcription(self, message: MessageObject, api_key: str):
        """Обрабатывает расшифровку в фоновом потоке"""
        try:
            # Получаем документ из сообщения
            document = self._get_document_from_message(message)
            if not document:
                self._show_error(locali.get_string("NOT_VOICE_MESSAGE"))
                return

            # Загружаем файл
            audio_path = self._download_audio_file(document, message)
            if not audio_path:
                self._show_error(locali.get_string("DOWNLOAD_ERROR"))
                return

            # Конвертируем в base64
            audio_base64 = self._convert_to_base64(audio_path)
            if not audio_base64:
                self._show_error("Failed to convert audio to base64")
                return

            # Определяем MIME тип
            mime_type = self._get_mime_type(audio_path)

            # Отправляем на обработку в Gemini
            response_text = self._process_with_gemini(audio_base64, mime_type, api_key)
            
            # Удаляем временный файл
            try:
                os.remove(audio_path)
            except:
                pass

            if response_text:
                # Парсим ответ на три блока
                short_summary, detailed_summary, transcript = self._parse_gemini_response(response_text)
                
                # Сохраняем результаты
                self.current_short_summary = short_summary
                self.current_detailed_summary = detailed_summary
                self.current_transcript = transcript

                # Показываем результат в зависимости от настройки
                display_type = self._get_default_display_type()
                if display_type == "detailed":
                    self._show_detailed_summary()
                elif display_type == "transcript":
                    self._show_transcript()
                else:  # "short" или любое другое значение
                    self._show_summary_result()
            else:
                self._show_error("Empty response from Gemini")

        except Exception as e:
            log(f"Error in transcription process: {e}")
            self._show_error(f"Processing error: {e}")
        finally:
            # Важно: всегда сбрасываем флаг обработки
            self.is_processing = False
            log("Transcription process completed")

    def _download_audio_file(self, document, message: MessageObject):
        """Загружает аудиофайл и возвращает путь к нему"""
        try:
            if document is None:
                log("Document is None, cannot download.")
                return None
            
            file_loader = get_file_loader()
            
            # Голосовые сообщения и кружки обрабатываются по-разному
            if message.isVoice() or message.isRoundVideo():
                log(f"Getting path for voice/round message using getPathToMessage")
                file_path_obj = file_loader.getPathToMessage(message.messageOwner)
            else:
                log(f"Getting path for other document type using getPathToAttach")
                file_path_obj = file_loader.getPathToAttach(document, True)

            if file_path_obj is None:
                log("Get path returned null.")
                return None
                
            file_path = file_path_obj.getAbsolutePath()
            log(f"Expected file path: {file_path}")
            
            if not file_path:
                log("File path is empty or null after getAbsolutePath.")
                return None

            # Ждем загрузки файла
            if self._wait_for_file(file_path, document, message):
                return file_path
            else:
                log("Failed to download file after waiting.")
                return None
                
        except Exception as e:
            log(f"Error downloading audio file: {e}")
            import traceback
            log(f"Traceback: {traceback.format_exc()}")
            return None

    def _convert_to_base64(self, file_path: str):
        """Конвертирует аудиофайл в base64"""
        try:
            with open(file_path, 'rb') as f:
                audio_data = f.read()
                return base64.b64encode(audio_data).decode('utf-8')
        except Exception as e:
            log(f"Error converting to base64: {e}")
            return None

    def _get_mime_type(self, file_path: str) -> str:
        """Определяет MIME тип аудиофайла"""
        ext = os.path.splitext(file_path)[1].lower()
        
        mime_types = {
            '.ogg': 'audio/ogg',
            '.opus': 'audio/ogg',
            '.mp3': 'audio/mpeg',
            '.wav': 'audio/wav',
            '.m4a': 'audio/mp4',
            '.aac': 'audio/aac'
        }
        
        return mime_types.get(ext, 'audio/ogg')

    def _process_with_gemini(self, audio_base64: str, mime_type: str, api_key: str) -> Optional[str]:
        """Отправляет аудио на обработку через Gemini API"""
        try:
            url = f"{GEMINI_BASE_URL}?key={api_key}"
            
            # Формируем промпт с заменой переменной
            system_prompt = SYSTEM_PROMPT_AUDIO_VIDEO_SUMMARY_GEMINI.format(
                forwarder_name="Пользователь",
                transcripts_block="Аудиосообщение из Telegram."
            )
            
            payload = {
                "system_instruction": {
                    "parts": [{
                        "text": system_prompt
                    }]
                },
                "contents": [{
                    "role": "user",
                    "parts": [
                        {"text": "Создай краткую сводку, подробную сводку и отформатированную расшифровку этого голосового сообщения."},
                        {"inline_data": {
                            "mime_type": mime_type,
                            "data": audio_base64
                        }}
                    ]
                }],
                "generationConfig": {
                    "temperature": 0
                }
            }

            response = self.session.post(url, json=payload, timeout=90)
            response.raise_for_status()

            data = response.json()
            
            if "candidates" in data and data["candidates"]:
                candidate = data["candidates"][0]
                if "content" in candidate and "parts" in candidate["content"]:
                    parts = candidate["content"]["parts"]
                    if parts and "text" in parts[0]:
                        return parts[0]["text"].strip()

            # Если есть ошибка в ответе
            if "error" in data:
                error_msg = data["error"].get("message", "Unknown API error")
                log(f"Gemini API error: {error_msg}")
                return None

            log("No text found in API response")
            return None

        except requests.exceptions.RequestException as e:
            log(f"Request error: {e}")
            return None
        except Exception as e:
            log(f"Gemini API error: {e}")
            return None

    def _show_summary_result(self):
        """Показывает краткую сводку с кнопками переключения"""
        def show_result():
            try:
                current_fragment = get_last_fragment()
                if not current_fragment or not current_fragment.getParentActivity():
                    return

                context = current_fragment.getParentActivity()
                builder = AlertDialogBuilder(context, AlertDialogBuilder.ALERT_TYPE_MESSAGE)
                builder.set_title(self._get_title_for_type("short"))
                builder.set_message(self.current_short_summary)
                
                # Кнопка "Подробнее"
                def show_detailed(b, w):
                    b.dismiss()
                    self._show_detailed_summary()

                # Кнопка "Расшифровка"
                def show_transcript(b, w):
                    b.dismiss()
                    self._show_transcript()

                builder.set_positive_button(locali.get_string("DETAILED_BUTTON"), show_detailed)
                builder.set_negative_button(locali.get_string("TRANSCRIPT_BUTTON"), show_transcript)
                builder.show()

            except Exception as e:
                log(f"Error showing summary result: {e}")
                BulletinHelper.show_error(f"Error showing result: {e}")

        run_on_ui_thread(show_result)

    def _show_detailed_summary(self):
        """Показывает подробную сводку"""
        def show_detailed():
            try:
                current_fragment = get_last_fragment()
                if not current_fragment or not current_fragment.getParentActivity():
                    return

                context = current_fragment.getParentActivity()
                builder = AlertDialogBuilder(context, AlertDialogBuilder.ALERT_TYPE_MESSAGE)
                builder.set_title(self._get_title_for_type("detailed"))
                builder.set_message(self.current_detailed_summary)
                
                # Кнопка "Краткая сводка"
                def show_short(b, w):
                    b.dismiss()
                    self._show_summary_result()

                # Кнопка "Расшифровка"
                def show_transcript(b, w):
                    b.dismiss()
                    self._show_transcript()

                builder.set_positive_button(locali.get_string("SHORT_BUTTON"), show_short)
                builder.set_negative_button(locali.get_string("TRANSCRIPT_BUTTON"), show_transcript)
                builder.show()

            except Exception as e:
                log(f"Error showing detailed summary: {e}")

        run_on_ui_thread(show_detailed)

    def _show_transcript(self):
        """Показывает расшифровку"""
        def show_transcript():
            try:
                current_fragment = get_last_fragment()
                if not current_fragment or not current_fragment.getParentActivity():
                    return

                context = current_fragment.getParentActivity()
                builder = AlertDialogBuilder(context, AlertDialogBuilder.ALERT_TYPE_MESSAGE)
                builder.set_title(self._get_title_for_type("transcript"))
                builder.set_message(self.current_transcript)
                
                # Кнопка "Краткая сводка"
                def show_short(b, w):
                    b.dismiss()
                    self._show_summary_result()

                # Кнопка "Подробнее"
                def show_detailed(b, w):
                    b.dismiss()
                    self._show_detailed_summary()

                builder.set_positive_button(locali.get_string("SHORT_BUTTON"), show_short)
                builder.set_negative_button(locali.get_string("DETAILED_BUTTON"), show_detailed)
                builder.show()

            except Exception as e:
                log(f"Error showing transcript: {e}")

        run_on_ui_thread(show_transcript)

    def _show_error(self, error_message: str):
        """Показывает ошибку пользователю"""
        run_on_ui_thread(lambda: BulletinHelper.show_error(
            locali.get_string("TRANSCRIPTION_ERROR").format(error=error_message)
        ))

    def _open_api_key_link(self, view):
        """Открывает ссылку для получения API ключа"""
        try:
            from android.content import Intent
            from android.net import Uri
            
            current_fragment = get_last_fragment()
            if not current_fragment or not current_fragment.getParentActivity():
                return
                
            context = current_fragment.getParentActivity()
            intent = Intent(Intent.ACTION_VIEW, Uri.parse("https://aistudio.google.com/app/apikey"))
            context.startActivity(intent)
        except Exception as e:
            log(f"Error opening API key link: {e}")

    def _show_usage_info(self, view):
        """Показывает информацию об использовании"""
        try:
            current_fragment = get_last_fragment()
            if not current_fragment or not current_fragment.getParentActivity():
                return

            context = current_fragment.getParentActivity()
            builder = AlertDialogBuilder(context, AlertDialogBuilder.ALERT_TYPE_MESSAGE)
            builder.set_title(locali.get_string("USAGE_TITLE"))
            builder.set_message(locali.get_string("USAGE_TEXT"))
            builder.set_positive_button(locali.get_string("CLOSE_BUTTON"), lambda b, w: b.dismiss())
            builder.show()
        except Exception as e:
            log(f"Error showing usage info: {e}")

    def _on_enable_change(self, enabled: bool):
        """Обработка изменения состояния плагина"""
        log(f"Plugin enable state changed to: {enabled}")
        if enabled:
            log("Adding menu item due to enable state change...")
            self._add_menu_item()
        else:
            log("Plugin disabled, removing menu item...")
            try:
                self.remove_menu_item(self.menu_item_id)
            except:
                pass

    def _on_display_type_click(self, display_type: str):
        """Обработка выбора типа отображения"""
        def handle_click(view):
            self._set_default_display_type(display_type)
            log(f"Default display type changed to: {display_type}")
        return handle_click

    def create_settings(self):
        """Создает настройки плагина"""
        current_type = self._get_default_display_type()

        return [
            Header(text=locali.get_string("SETTINGS_HEADER")),

            Switch(
                key="enabled",
                text=locali.get_string("ENABLE_PLUGIN"),
                subtext=locali.get_string("ENABLE_PLUGIN_SUBTEXT"),
                default=True,
                icon="msg_voice_mini",
                on_change=self._on_enable_change
            ),

            Input(
                key="api_key",
                text=locali.get_string("API_KEY_INPUT"),
                subtext=locali.get_string("API_KEY_SUBTEXT"),
                default="",
                icon="msg_pin_code"
            ),

            Text(
                text=locali.get_string("GET_API_KEY_BUTTON"),
                icon="msg_link",
                accent=True,
                on_click=self._open_api_key_link
            ),

            Divider(),

            Header(text=locali.get_string("DEFAULT_DISPLAY_HEADER")),

            Text(
                text=locali.get_string("DEFAULT_DISPLAY_SHORT") + (" ✓" if current_type == "short" else ""),
                icon="msg_voice_mini",
                accent=current_type == "short",
                on_click=self._on_display_type_click("short")
            ),

            Text(
                text=locali.get_string("DEFAULT_DISPLAY_DETAILED") + (" ✓" if current_type == "detailed" else ""),
                icon="msg_voice_mini",
                accent=current_type == "detailed",
                on_click=self._on_display_type_click("detailed")
            ),

            Text(
                text=locali.get_string("DEFAULT_DISPLAY_TRANSCRIPT") + (" ✓" if current_type == "transcript" else ""),
                icon="msg_voice_mini",
                accent=current_type == "transcript",
                on_click=self._on_display_type_click("transcript")
            ),

            Divider(),

            Text(
                text=locali.get_string("USAGE_TITLE"),
                icon="msg_info",
                on_click=self._show_usage_info
            )
        ]